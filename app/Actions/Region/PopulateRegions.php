<?php

namespace App\Actions\Region;

use App\Http\Integrations\Ibge\Services\IbgeLocationService;
use App\Models\City;
use App\Models\Region;
use App\Models\State;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class PopulateRegions
{
    use AsAction;

    public function handle(): void
    {
        $ibgeLocationService = new IbgeLocationService();

        foreach ($ibgeLocationService->getRegions() as $region) {
            try {
                Region::updateOrCreate([
                    'id' => $region->id,
                ], [
                    'abbreviation' => $region->sigla,
                    'name' => $region->nome
                ]);
            } catch (Throwable $th) {
                error($th);
            }
        }

        foreach ($ibgeLocationService->getStates() as $state) {
            try {
                State::updateOrCreate([
                    'id' => $state->id,
                ], [
                    'region_id' => $state->regiao->id,
                    'abbreviation' => $state->sigla,
                    'name' => $state->nome
                ]);
            } catch (Throwable $th) {
                error($th);
            }
        }

        foreach ($ibgeLocationService->getCities() as $city) {
            try {
                City::updateOrCreate([
                    'id' => $city->id,
                ], [
                    'state_id' => $city->microrregiao->mesorregiao->UF->id,
                    'name' => $city->nome
                ]);
            } catch (Throwable $th) {
                error($th);
            }
        }
    }
}
