<?php

namespace App\Actions\Applicant;

use App\Models\Applicant;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class DeleteApplicant
{
    use AsAction;

    public function handle(Applicant $applicant): void
    {
        try {
            DB::transaction(function () use ($applicant) {
                $applicant->applicantContacts()->delete();
                $applicant->delete();
            });
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
