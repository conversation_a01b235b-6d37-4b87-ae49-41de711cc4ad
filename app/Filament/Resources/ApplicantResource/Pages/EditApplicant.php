<?php

namespace App\Filament\Resources\ApplicantResource\Pages;

use App\Actions\Applicant\DeleteApplicant;
use App\Filament\Resources\ApplicantResource;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;
use Throwable;

class EditApplicant extends EditRecord
{
    protected static string $resource = ApplicantResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make()
                ->using(function () {
                    try {
                        DeleteApplicant::run($this->record);
                        success_notification(__('applicants.responses.delete.success'))->send();
                        return redirect()->route('filament.app.resources.applicants.index');
                    } catch (Throwable) {
                        error_notification(__('applicants.responses.delete.error'))->send();
                    }
                }),
        ];
    }

    public function hasCombinedRelationManagerTabsWithContent(): bool
    {
        return true;
    }

    protected function getSavedNotification(): ?Notification
    {
        return success_notification(__('applicants.responses.update.success'));
    }
}
