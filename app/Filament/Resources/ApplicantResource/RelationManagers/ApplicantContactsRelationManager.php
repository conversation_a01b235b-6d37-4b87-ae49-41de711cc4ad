<?php

namespace App\Filament\Resources\ApplicantResource\RelationManagers;

use App\Core\Filament\Filters\TableTextFilter;
use App\Enums\ApplicantContactTypeEnum;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class ApplicantContactsRelationManager extends RelationManager
{
    protected static string $relationship = 'applicantContacts';
    protected static ?string $recordTitleAttribute = 'value';
    protected static ?string $title = 'Contatos';
    protected static ?string $modelLabel = 'contato';

    public function form(Form $form): Form
    {
        return $form->schema([
            Grid::make(1)->schema([
                Toggle::make('is_primary')
                    ->label(__('applicant_contacts.forms.fields.is_primary')),
            ]),
            Grid::make(4)->schema([
                Select::make('type')
                    ->label(__('applicant_contacts.forms.fields.type'))
                    ->required()
                    ->options(ApplicantContactTypeEnum::getTranslated()),
                TextInput::make('value')
                    ->label(__('applicant_contacts.forms.fields.value'))
                    ->required()
                    ->maxLength(255)
                    ->columnSpan(3),
            ]),
        ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('type')
                    ->label(__('applicant_contacts.forms.fields.type'))
                    ->formatStateUsing(fn($state) => ApplicantContactTypeEnum::from($state)->label()),
                Tables\Columns\TextColumn::make('value')
                    ->label(__('applicant_contacts.forms.fields.value')),
                Tables\Columns\IconColumn::make('is_primary')
                    ->label(__('applicant_contacts.forms.fields.is_primary'))
                    ->boolean(),
            ])
            ->filters([
                TableTextFilter::buildLike('applicant_contacts', 'value'),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\EditAction::make(),
                    Tables\Actions\DeleteAction::make(),
                ]),
            ]);
    }
}
