<?php

namespace App\Filament\Resources\ApplicantResource\RelationManagers;

use App\Core\Filament\Filters\TableTextFilter;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class ApplicantDependantsRelationManager extends RelationManager
{
    protected static string $relationship = 'applicantDependants';
    protected static ?string $title = 'Dependentes';
    protected static ?string $modelLabel = 'dependente';
    protected static ?string $pluralModelLabel = 'dependentes';

    public function form(Form $form): Form
    {
        return $form->schema([
            Grid::make(4)->schema([
                TextInput::make('full_name')
                    ->label(__('applicant_dependants.forms.fields.full_name'))
                    ->required()
                    ->columnSpan(3),
                TextInput::make('cpf')
                    ->label(__('applicant_dependants.forms.fields.cpf'))
                    ->required(),
            ]),
            Grid::make(4)->schema([
                TextInput::make('rg')
                    ->label(__('applicant_dependants.forms.fields.rg')),
                TextInput::make('rg_issuing_agency')
                    ->label(__('applicant_dependants.forms.fields.rg_issuing_agency')),
                DatePicker::make('rg_issuing_date')
                    ->label(__('applicant_dependants.forms.fields.rg_issuing_date')),
                TextInput::make('gender')
                    ->label(__('applicant_dependants.forms.fields.gender')),
            ]),
            Grid::make(4)->schema([
                TextInput::make('other_document_type')
                    ->label(__('applicant_dependants.forms.fields.other_document_type')),
                TextInput::make('other_document_number')
                    ->label(__('applicant_dependants.forms.fields.other_document_number')),
            ]),
        ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('full_name')
                    ->label(__('applicant_dependants.forms.fields.full_name')),
                TextColumn::make('cpf')
                    ->label(__('applicant_dependants.forms.fields.cpf')),
            ])
            ->filters([
                TableTextFilter::buildLike('applicant_dependants', 'full_name'),
                TableTextFilter::buildLike('applicant_dependants', 'cpf'),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->successNotification(success_notification(__('applicant_dependants.responses.create.success'))),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\EditAction::make()
                        ->successNotification(success_notification(__('applicant_dependants.responses.update.success'))),
                    Tables\Actions\DeleteAction::make()
                        ->successNotification(success_notification(__('applicant_dependants.responses.delete.success'))),
                ]),
            ]);
    }
}
