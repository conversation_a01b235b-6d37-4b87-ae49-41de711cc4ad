<?php

namespace App\Filament\Resources\ApplicantResource\RelationManagers;

use App\Core\Filament\Filters\TableTextFilter;
use Filament\Forms;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ApplicantSpousesRelationManager extends RelationManager
{
    protected static string $relationship = 'applicantSpouses';
    protected static ?string $recordTitleAttribute = 'full_name';
    protected static ?string $title = 'Cônjuges';
    protected static ?string $modelLabel = 'cônjuge';
    protected static ?string $pluralModelLabel = 'cônjuges';

    public function form(Form $form): Form
    {
        return $form->schema([
            Grid::make(4)->schema([
                TextInput::make('full_name')
                    ->label(__('applicant_spouses.forms.fields.full_name'))
                    ->required()
                    ->columnSpan(3),
                TextInput::make('cpf')
                    ->label(__('applicant_spouses.forms.fields.cpf'))
                    ->required(),
            ]),
            Grid::make(4)->schema([
                TextInput::make('rg')
                    ->label(__('applicant_spouses.forms.fields.rg')),
                TextInput::make('rg_issuing_agency')
                    ->label(__('applicant_spouses.forms.fields.rg_issuing_agency')),
                DatePicker::make('rg_issuing_date')
                    ->label(__('applicant_spouses.forms.fields.rg_issuing_date')),
                DatePicker::make('birth_date')
                    ->label(__('applicant_spouses.forms.fields.birth_date')),
            ]),
            Grid::make(4)->schema([
                TextInput::make('other_document_type')
                    ->label(__('applicant_spouses.forms.fields.other_document_type')),
                TextInput::make('other_document_number')
                    ->label(__('applicant_spouses.forms.fields.other_document_number')),
                DatePicker::make('relationship_started_at')
                    ->label(__('applicant_spouses.forms.fields.relationship_started_at')),
                DatePicker::make('relationship_ended_at')
                    ->label(__('applicant_spouses.forms.fields.relationship_ended_at')),
            ]),
            Grid::make(4)->schema([
                TextInput::make('place_of_birth')
                    ->label(__('applicant_spouses.forms.fields.place_of_birth')),
                TextInput::make('nationality')
                    ->label(__('applicant_spouses.forms.fields.nationality')),
                TextInput::make('gender')
                    ->label(__('applicant_spouses.forms.fields.gender')),
            ]),
        ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('full_name')
                    ->label(__('applicant_spouses.forms.fields.full_name')),
                TextColumn::make('cpf')
                    ->label(__('applicant_spouses.forms.fields.cpf')),
            ])
            ->filters([
                TableTextFilter::buildLike('applicant_spouses', 'full_name'),
                TableTextFilter::buildLike('applicant_spouses', 'cpf'),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->successNotification(success_notification(__('applicant_spouses.responses.create.success'))),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\EditAction::make()
                        ->successNotification(success_notification(__('applicant_spouses.responses.update.success'))),
                    Tables\Actions\DeleteAction::make()
                        ->successNotification(success_notification(__('applicant_spouses.responses.delete.success'))),
                ]),
            ]);
    }
}
