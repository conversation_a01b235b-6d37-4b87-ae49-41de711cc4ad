<?php

namespace App\Filament\Resources;

use App\Actions\Applicant\DeleteApplicant;
use App\Core\Filament\Filters\TableTextFilter;
use App\Enums\ApplicantMaritalStatusEnum;
use App\Enums\ApplicantOtherDocumentTypeEnum;
use App\Filament\Resources\ApplicantResource\Pages;
use App\Filament\Resources\ApplicantResource\RelationManagers\ApplicantContactsRelationManager;
use App\Filament\Resources\ApplicantResource\RelationManagers\ApplicantDependantsRelationManager;
use App\Filament\Resources\ApplicantResource\RelationManagers\ApplicantSpousesRelationManager;
use App\Http\Integrations\ViaCep\Services\ViaCepZipcodeService;
use App\Models\Applicant;
use App\Models\City;
use App\Models\State;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Tabs\Tab;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms\Set;
use Filament\Resources\Resource;
use Filament\Support\RawJs;
use Filament\Tables;
use Filament\Tables\Table;
use Throwable;

class ApplicantResource extends Resource
{
    protected static ?string $model = Applicant::class;
    protected static ?string $modelLabel = 'requerente';
    protected static ?string $pluralModelLabel = 'requerentes';
    protected static ?string $navigationGroup = 'Gestão de requerentes';
    protected static ?string $navigationIcon = 'heroicon-o-user-group';

    public static function form(Form $form): Form
    {
        return $form->schema([
            Grid::make(1)->schema([
                Tabs::make()->schema([
                    Tab::make('Dados pessoais')->schema([
                        Grid::make(4)->schema([
                            TextInput::make('full_name')
                                ->label(__('applicants.forms.fields.full_name'))
                                ->required()
                                ->columnSpan(3),
                            TextInput::make('cpf')
                                ->required()
                                ->label(__('applicants.forms.fields.cpf')),
                        ]),
                        Grid::make(4)->schema([
                            TextInput::make('rg')
                                ->label(__('applicants.forms.fields.rg')),
                            TextInput::make('rg_issuing_agency')
                                ->label(__('applicants.forms.fields.rg_issuing_agency')),
                            DatePicker::make('rg_issuing_date')
                                ->label(__('applicants.forms.fields.rg_issuing_date')),
                            DatePicker::make('birth_date')
                                ->label(__('applicants.forms.fields.birth_date')),
                        ]),
                        Grid::make(4)->schema([
                            Select::make('other_document_type')
                                ->label(__('applicants.forms.fields.other_document_type'))
                                ->options(ApplicantOtherDocumentTypeEnum::getTranslated()),
                            TextInput::make('other_document_number')
                                ->label(__('applicants.forms.fields.other_document_number')),
                            Select::make('marital_status')
                                ->label(__('applicants.forms.fields.marital_status'))
                                ->options(ApplicantMaritalStatusEnum::getTranslated()),
                            DatePicker::make('marital_status_date')
                                ->label(__('applicants.forms.fields.marital_status_date')),
                        ]),
                        Grid::make(4)->schema([
                            TextInput::make('place_of_birth')
                                ->label(__('applicants.forms.fields.place_of_birth')),
                            TextInput::make('nationality')
                                ->label(__('applicants.forms.fields.nationality')),
                            TextInput::make('gender')
                                ->label(__('applicants.forms.fields.gender')),
                            TextInput::make('job_title')
                                ->label(__('applicants.forms.fields.job_title')),
                        ]),
                    ]),
                    Tab::make('Endereço')->schema([
                        Grid::make(4)->schema([
                            TextInput::make('current_address_zipcode')
                                ->label(__('applicants.forms.fields.current_address_zipcode'))
                                ->mask('99999-999')
                                ->lazy()
                                ->afterStateUpdated(function (?string $state, Set $set) {
                                    if (is_null($state) || trim($state) === '') {
                                        return;
                                    }

                                    try {
                                        $fullAddress = ViaCepZipcodeService::make()->getZipcodeDetails($state);
                                    } catch (Throwable $th) {
                                        error($th);
                                        error_notification('Não foi possível obter os dados automáticos do CEP.')->send();

                                        $set('current_address_street', null);
                                        $set('current_address_district', null);
                                        $set('current_address_city_id', null);
                                        $set('current_address_city', null);
                                        $set('current_address_state_id', null);
                                        $set('current_address_state', null);

                                        return;
                                    }

                                    if (isset($fullAddress->erro) && $fullAddress->erro) {
                                        $set('current_address_street', null);
                                        $set('current_address_district', null);
                                        $set('current_address_city_id', null);
                                        $set('current_address_city', null);
                                        $set('current_address_state_id', null);
                                        $set('current_address_state', null);
                                        return;
                                    }

                                    /** @var \App\Models\City $city */
                                    $city = City::find($fullAddress->ibge);

                                    /** @var \App\Models\State $addressState */
                                    $addressState = State::query()
                                        ->where('abbreviation', $fullAddress->uf)
                                        ->first();

                                    $set('current_address_street', $fullAddress->logradouro);
                                    $set('current_address_district', $fullAddress->bairro);
                                    $set('current_address_city_id', $city->id);
                                    $set('current_address_city', $city->name);
                                    $set('current_address_state_id', $addressState->id);
                                    $set('current_address_state', $addressState->abbreviation);
                                }),
                        ]),
                        Grid::make(4)->schema([
                            TextInput::make('current_address_street')
                                ->label(__('applicants.forms.fields.current_address_street'))
                                ->columnSpan(2),
                            TextInput::make('current_address_number')
                                ->label(__('applicants.forms.fields.current_address_number')),
                            TextInput::make('current_address_complement')
                                ->label(__('applicants.forms.fields.current_address_complement')),
                        ]),
                        Grid::make(4)->schema([
                            TextInput::make('current_address_district')
                                ->label(__('applicants.forms.fields.current_address_district')),
                            Hidden::make('current_address_city_id'),
                            TextInput::make('current_address_city')
                                ->label(__('applicants.forms.fields.current_address_city_id'))
                                ->readOnly(),
                            Hidden::make('current_address_state_id'),
                            TextInput::make('current_address_state')
                                ->label(__('applicants.forms.fields.current_address_state_id'))
                                ->readOnly(),
                        ]),
                    ]),
                    Tab::make('Renda')->schema([
                        Grid::make(4)->schema([
                            TextInput::make('monthly_income')
                                ->label(__('applicants.forms.fields.monthly_income'))
                                ->mask(fn(): RawJs => RawJs::make(<<<'JS'
                                    'R$ ' + $money($input, ',', '', 2)
                                JS)),
                        ]),
                        Grid::make(1)->schema([
                            Textarea::make('income_additional_information')
                                ->label(__('applicants.forms.fields.income_additional_information'))
                                ->rows(5),
                        ]),
                    ]),
                    Tab::make('Cadastro Único (CadÚnico)')->schema([
                        Grid::make(4)->schema([
                            TextInput::make('cadunico_nis')
                                ->label(__('applicants.forms.fields.cadunico_nis'))
                                ->columnSpan(3),
                            DatePicker::make('cadunico_update_date')
                                ->label(__('applicants.forms.fields.cadunico_update_date')),
                        ]),
                        Grid::make(4)->schema([
                            TextInput::make('cadunico_family_code')
                                ->label(__('applicants.forms.fields.cadunico_family_code')),
                            TextInput::make('cadunico_family_member_count')
                                ->label(__('applicants.forms.fields.cadunico_family_member_count'))
                                ->numeric(),
                            TextInput::make('cadunico_per_capita_income')
                                ->label(__('applicants.forms.fields.cadunico_per_capita_income'))
                                ->mask(fn(): RawJs => RawJs::make(<<<'JS'
                                    'R$ ' + $money($input, ',', '', 2)
                                JS)),
                        ]),
                    ]),
                ]),
            ]),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('full_name')
                    ->sortable()
                    ->label(__('applicants.forms.fields.full_name')),
                Tables\Columns\TextColumn::make('cpf')
                    ->sortable()
                    ->label(__('applicants.forms.fields.cpf')),
                Tables\Columns\TextColumn::make('cadunico_nis')
                    ->sortable()
                    ->label(__('applicants.forms.fields.cadunico_nis')),
            ])
            ->filters([
                TableTextFilter::buildLike('applicants', 'full_name'),
                TableTextFilter::buildLike('applicants', 'cpf'),
                TableTextFilter::buildLike('applicants', 'cadunico_nis'),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\EditAction::make()
                        ->successNotification(success_notification(__('applicants.responses.update.success'))),
                    Tables\Actions\DeleteAction::make()
                        ->action(function (Applicant $applicant) {
                            try {
                                DeleteApplicant::run($applicant);
                                success_notification(__('applicants.responses.delete.success'))->send();
                            } catch (Throwable) {
                                error_notification(__('applicants.responses.delete.error'))->send();
                            }
                        }),
                ]),
            ])
            ->emptyStateHeading('Ainda sem requerentes cadastrados.')
            ->emptyStateDescription('Assim que você cadastrar seus requerentes, eles aparecerão nesta listagem.');
    }

    public static function getRelations(): array
    {
        return [
            ApplicantContactsRelationManager::class,
            ApplicantSpousesRelationManager::class,
            ApplicantDependantsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListApplicants::route('/'),
            'create' => Pages\CreateApplicant::route('/create'),
            'edit' => Pages\EditApplicant::route('/{record}/edit'),
            'view' => Pages\ViewApplicant::route('/{record}'),
        ];
    }
}
