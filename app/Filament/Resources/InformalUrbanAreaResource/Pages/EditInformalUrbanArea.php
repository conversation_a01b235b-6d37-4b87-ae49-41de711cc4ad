<?php

namespace App\Filament\Resources\InformalUrbanAreaResource\Pages;

use App\Filament\Resources\InformalUrbanAreaResource;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;
use Throwable;

class EditInformalUrbanArea extends EditRecord
{
    protected static string $resource = InformalUrbanAreaResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make()
                ->action(function () {
                    try {
                        $this->record->delete();
                        success_notification(__('informal_urban_areas.responses.delete.success'))->send();
                        return redirect()->route('filament.app.resources.informal-urban-areas.index');
                    } catch (Throwable) {
                        error_notification(__('informal_urban_areas.responses.delete.error'))->send();
                    }
                }),
        ];
    }

    public function hasCombinedRelationManagerTabsWithContent(): bool
    {
        return true;
    }

    protected function getSavedNotification(): ?Notification
    {
        return success_notification(__('informal_urban_areas.responses.update.success'));
    }
}
