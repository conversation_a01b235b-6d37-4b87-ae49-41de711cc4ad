<?php

namespace App\Filament\Resources\InformalUrbanAreaResource\Pages;

use App\Filament\Resources\InformalUrbanAreaResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewInformalUrbanArea extends ViewRecord
{
    protected static string $resource = InformalUrbanAreaResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }

    public function hasCombinedRelationManagerTabsWithContent(): bool
    {
        return true;
    }
}
