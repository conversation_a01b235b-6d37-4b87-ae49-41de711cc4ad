<?php

namespace App\Filament\Resources\InformalUrbanAreaResource\Pages;

use App\Filament\Resources\InformalUrbanAreaResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListInformalUrbanAreas extends ListRecords
{
    protected static string $resource = InformalUrbanAreaResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
