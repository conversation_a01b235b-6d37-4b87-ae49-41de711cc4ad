<?php

namespace App\Filament\Resources\InformalUrbanAreaResource\Pages;

use App\Filament\Resources\InformalUrbanAreaResource;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;

class CreateInformalUrbanArea extends CreateRecord
{
    protected static string $resource = InformalUrbanAreaResource::class;

    protected function getCreatedNotification(): ?Notification
    {
        return success_notification(__('informal_urban_areas.responses.create.success'));
    }
}
