<?php

namespace App\Filament\Resources\InformalUrbanAreaResource\RelationManagers;

use App\Http\Integrations\ViaCep\Services\ViaCepZipcodeService;
use App\Models\City;
use App\Models\State;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Tabs\Tab;
use Filament\Forms\Form;
use Filament\Forms\Set;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Throwable;

class PlotsRelationManager extends RelationManager
{
    protected static string $relationship = 'plots';
    protected static ?string $title = 'Lotes';
    protected static ?string $inverseRelationship = 'informalUrbanArea';

    public function form(Form $form): Form
    {
        return $form->schema([
            Grid::make(1)->schema([
                Tabs::make()->schema([
                    Tab::make('Identificação')->schema([
                        Grid::make(2)->schema([
                            TextInput::make('identification')
                                ->label(__('plots.forms.fields.identification'))
                                ->required()
                                ->maxLength(255),
                            TextInput::make('original_registration_number')
                                ->label(__('plots.forms.fields.original_registration_number'))
                                ->maxLength(255),
                        ]),
                        Grid::make(1)->schema([
                            TextInput::make('property_registry_office')
                                ->label(__('plots.forms.fields.property_registry_office'))
                                ->maxLength(255),
                        ])
                    ]),
                    Tab::make('Endereço')->schema([
                        Grid::make(4)->schema([
                            TextInput::make('zipcode')
                                ->label(__('plots.forms.fields.zipcode'))
                                ->mask('99999-999')
                                ->lazy()
                                ->afterStateUpdated(function (?string $state, Set $set) {
                                    if (is_null($state) || trim($state) === '') {
                                        return;
                                    }

                                    try {
                                        $fullAddress = ViaCepZipcodeService::make()->getZipcodeDetails($state);
                                    } catch (Throwable $th) {
                                        error($th);
                                        error_notification('Não foi possível obter os dados automáticos do CEP.')->send();

                                        $set('street', null);
                                        $set('district', null);
                                        $set('city_id', null);
                                        $set('city', null);
                                        $set('state_id', null);
                                        $set('state', null);

                                        return;
                                    }

                                    if (isset($fullAddress->erro) && $fullAddress->erro) {
                                        $set('street', null);
                                        $set('district', null);
                                        $set('city_id', null);
                                        $set('city', null);
                                        $set('state_id', null);
                                        $set('state', null);
                                        return;
                                    }

                                    /** @var \App\Models\City $city */
                                    $city = City::find($fullAddress->ibge);

                                    /** @var \App\Models\State $addressState */
                                    $addressState = State::query()
                                        ->where('abbreviation', $fullAddress->uf)
                                        ->first();

                                    $set('street', $fullAddress->logradouro);
                                    $set('district', $fullAddress->bairro);
                                    $set('city_id', $city->id);
                                    $set('city', $city->name);
                                    $set('state_id', $addressState->id);
                                    $set('state', $addressState->abbreviation);
                                }),
                        ]),
                        Grid::make(4)->schema([
                            TextInput::make('street')
                                ->label(__('plots.forms.fields.street'))
                                ->columnSpan(2),
                            TextInput::make('number')
                                ->label(__('plots.forms.fields.number')),
                            TextInput::make('complement')
                                ->label(__('plots.forms.fields.complement')),
                        ]),
                        Grid::make(4)->schema([
                            TextInput::make('district')
                                ->label(__('plots.forms.fields.district')),
                            Hidden::make('city_id'),
                            TextInput::make('city')
                                ->label(__('plots.forms.fields.city_id'))
                                ->readOnly(),
                            Hidden::make('state_id'),
                            TextInput::make('state')
                                ->label(__('plots.forms.fields.state_id'))
                                ->readOnly(),
                        ]),
                    ]),
                ]),
            ]),
        ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('identification')
            ->modelLabel('lote')
            ->pluralModelLabel('lotes')
            ->columns([
                Tables\Columns\TextColumn::make('identification'),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
