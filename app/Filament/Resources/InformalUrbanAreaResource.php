<?php

namespace App\Filament\Resources;

use App\Core\Filament\Filters\TableTextFilter;
use App\Enums\InformalUrbanAreaRegularizationStatusEnum;
use App\Enums\InformalUrbanAreaReurbTypeEnum;
use App\Filament\Resources\InformalUrbanAreaResource\Pages;
use App\Filament\Resources\InformalUrbanAreaResource\RelationManagers\PlotsRelationManager;
use App\Models\InformalUrbanArea;
use App\Models\State;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class InformalUrbanAreaResource extends Resource
{
    protected static ?string $model = InformalUrbanArea::class;
    protected static ?string $modelLabel = 'área urbana informal';
    protected static ?string $pluralModelLabel = 'áreas urbanas informais';
    protected static ?string $navigationGroup = 'Gestão de imóveis/lotes';
    protected static ?string $navigationIcon = 'heroicon-o-square-3-stack-3d';

    public static function form(Form $form): Form
    {
        return $form->schema([
            Grid::make(4)->schema([
                Select::make('reurb_type')
                    ->label(__('informal_urban_areas.forms.fields.reurb_type'))
                    ->options(InformalUrbanAreaReurbTypeEnum::getTranslated()),
                TextInput::make('regularization_status')
                    ->label(__('informal_urban_areas.forms.fields.regularization_status'))
                    ->columnStart(4)
                    ->hiddenOn('create')
                    ->disabled()
                    ->default(InformalUrbanAreaRegularizationStatusEnum::IN_ANALYSIS->value)
                    ->formatStateUsing(fn($state) => InformalUrbanAreaRegularizationStatusEnum::from($state)->label()),
            ]),
            Grid::make(4)->schema([
                TextInput::make('name')
                    ->label(__('informal_urban_areas.forms.fields.name'))
                    ->required()
                    ->columnSpan(2),
                TextInput::make('identification_code')
                    ->label(__('informal_urban_areas.forms.fields.identification_code')),
                DatePicker::make('estimated_creation_date')
                    ->label(__('informal_urban_areas.forms.fields.estimated_creation_date')),
            ]),
            Grid::make(4)->schema([
                Select::make('state_id')
                    ->label(__('informal_urban_areas.forms.fields.state'))
                    ->lazy()
                    ->options(State::query()->orderBy('name')->get()->pluck('name', 'id')),
                Select::make('city_id')
                    ->label(__('informal_urban_areas.forms.fields.city'))
                    ->lazy()
                    ->options(function (callable $get) {
                        $state = State::find($get('state_id'));

                        if (! $state) {
                            return [];
                        }

                        return $state->cities()->orderBy('name')->get()->pluck('name', 'id');
                    }),
                TextInput::make('main_district')
                    ->label(__('informal_urban_areas.forms.fields.main_district')),
                TextInput::make('total_area_m2')
                    ->label(__('informal_urban_areas.forms.fields.total_area_m2'))
                    ->numeric(),
            ]),
            Grid::make(1)->schema([
                Textarea::make('additional_information')
                    ->label(__('informal_urban_areas.forms.fields.additional_information'))
                    ->rows(5),
            ]),
            Grid::make(1)->schema([
                Fieldset::make('Infraestrutura')->schema([
                    Grid::make(4)->schema([
                        Toggle::make('water_infrastructure_available')
                            ->label(__('informal_urban_areas.forms.fields.water_infrastructure_available')),
                        Toggle::make('sanitary_infrastructure_available')
                            ->label(__('informal_urban_areas.forms.fields.sanitary_infrastructure_available')),
                        Toggle::make('eletricity_infrastructure_available')
                            ->label(__('informal_urban_areas.forms.fields.eletricity_infrastructure_available')),
                        Toggle::make('road_infrastructure_available')
                            ->label(__('informal_urban_areas.forms.fields.road_infrastructure_available')),
                    ]),
                    Grid::make(1)->schema([
                        Textarea::make('infrastructure_additional_information')
                            ->label(__('informal_urban_areas.forms.fields.infrastructure_additional_information'))
                            ->rows(5),
                    ]),
                ]),
            ]),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label(__('informal_urban_areas.forms.fields.name')),
            ])
            ->filters([
                TableTextFilter::buildLike('informal_urban_areas', 'name'),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\EditAction::make(),
                    Tables\Actions\DeleteAction::make()
                        ->successNotification(success_notification(__('informal_urban_areas.responses.delete.success'))),
                ]),
            ])
            ->emptyStateHeading('Ainda sem áreas urbanas informais cadastradas.')
            ->emptyStateDescription('Assim que você cadastrar suas áreas urbanas informais, elas aparecerão nesta listagem.');;
    }

    public static function getRelations(): array
    {
        return [
            PlotsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListInformalUrbanAreas::route('/'),
            'create' => Pages\CreateInformalUrbanArea::route('/create'),
            'edit' => Pages\EditInformalUrbanArea::route('/{record}/edit'),
            'view' => Pages\ViewInformalUrbanArea::route('/{record}'),
        ];
    }
}
