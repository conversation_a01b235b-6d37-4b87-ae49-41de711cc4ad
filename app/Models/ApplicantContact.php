<?php

namespace App\Models;

use App\Models\Concerns\ApplicantContact\HandlesApplicantContactRelationships;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Applicant contact model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $applicant_id
 * @property  string $type
 * @property  string $value
 * @property  bool $is_primary
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 * @property  \Carbon\Carbon $deleted_at
 *
 * @property  \App\Models\Applicant $applicant
 */
class ApplicantContact extends Model
{
    use HandlesApplicantContactRelationships;
    use SoftDeletes;

    protected $fillable = [
        'applicant_id',
        'type',
        'value',
        'is_primary',
    ];
}
