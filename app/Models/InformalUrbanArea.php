<?php

namespace App\Models;

use App\Enums\InformalUrbanAreaRegularizationStatusEnum;
use App\Models\Concerns\InformalUrbanArea\HandlesInformalUrbanAreaRelationships;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Informal urban area model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  string $name
 * @property  string $identification_code
 * @property  string $description
 * @property  string $regularization_status
 * @property  string $reurb_type
 * @property  \Carbon\Carbon $estimated_creation_date
 * @property  int $city_id
 * @property  string $city
 * @property  int $state_id
 * @property  string $state
 * @property  string $main_district
 * @property  float $total_area_m2
 * @property  string $additional_information
 * @property  bool $water_infrastructure_available
 * @property  bool $sanitary_infrastructure_available
 * @property  bool $eletricity_infrastructure_available
 * @property  bool $road_infrastructure_available
 * @property  string $infrastructure_additional_information
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 * @property  \Carbon\Carbon $deleted_at
 *
 * @property  \Illuminate\Support\Collection|\App\Models\Plot[] $plots
 */
class InformalUrbanArea extends Model
{
    use SoftDeletes;
    use HandlesInformalUrbanAreaRelationships;

    protected $fillable = [
        'name',
        'identification_code',
        'description',
        'regularization_status',
        'reurb_type',
        'estimated_creation_date',
        'city_id',
        'city',
        'state_id',
        'state',
        'main_district',
        'total_area_m2',
        'additional_information',
        'water_infrastructure_available',
        'sanitary_infrastructure_available',
        'eletricity_infrastructure_available',
        'road_infrastructure_available',
        'infrastructure_additional_information',
    ];

    protected $casts = [
        'water_infrastructure_available' => 'bool',
        'sanitary_infrastructure_available' => 'bool',
        'eletricity_infrastructure_available' => 'bool',
        'road_infrastructure_available' => 'bool',
    ];

    public static function booted(): void
    {
        static::creating(function ($model) {
            $model->regularization_status = InformalUrbanAreaRegularizationStatusEnum::IN_ANALYSIS->value;
        });
    }
}
