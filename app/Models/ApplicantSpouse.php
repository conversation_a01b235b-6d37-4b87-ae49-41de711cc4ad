<?php

namespace App\Models;

use App\Models\Concerns\ApplicantSpouse\HandlesApplicantSpouseRelationships;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Applicant spouse model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $applicant_id
 * @property  string $full_name
 * @property  string $cpf
 * @property  string $rg
 * @property  string $rg_issuing_agency
 * @property  \Carbon\Carbon $rg_issuing_date
 * @property  string $other_document_type
 * @property  string $other_document_number
 * @property  \Carbon\Carbon $birth_date
 * @property  string $place_of_birth
 * @property  string $nationality
 * @property  string $gender
 * @property  \Carbon\Carbon $relationship_started_at
 * @property  \Carbon\Carbon $relationship_ended_at
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 * @property  \Carbon\Carbon $deleted_at
 *
 * @property  \App\Models\Applicant $applicant
 */
class ApplicantSpouse extends Model
{
    use HandlesApplicantSpouseRelationships;
    use SoftDeletes;

    protected $fillable = [
        'applicant_id',
        'full_name',
        'cpf',
        'rg',
        'rg_issuing_agency',
        'rg_issuing_date',
        'other_document_type',
        'other_document_number',
        'birth_date',
        'place_of_birth',
        'nationality',
        'gender',
        'relationship_started_at',
        'relationship_ended_at',
    ];
}
