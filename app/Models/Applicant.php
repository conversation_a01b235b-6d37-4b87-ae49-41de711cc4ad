<?php

namespace App\Models;

use App\Models\Concerns\Applicant\HandlesApplicantAttributes;
use App\Models\Concerns\Applicant\HandlesApplicantRelationships;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Applicant model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $user_id
 * @property  string $full_name
 * @property  string $cpf
 * @property  string $rg
 * @property  string $rg_issuing_agency
 * @property  \Carbon\Carbon $rg_issuing_date
 * @property  string $other_document_type
 * @property  string $other_document_number
 * @property  \Carbon\Carbon $birth_date
 * @property  string $place_of_birth
 * @property  string $nationality
 * @property  string $gender
 * @property  string $marital_status
 * @property  \Carbon\Carbon $marital_status_date
 * @property  string $job_title
 * @property  string $current_address_zipcode
 * @property  string $current_address_street
 * @property  string $current_address_number
 * @property  string $current_address_complement
 * @property  string $current_address_district
 * @property  int $current_address_city_id
 * @property  int $current_address_state_id
 * @property  float $monthly_income
 * @property  string $income_additional_information
 * @property  string $cadunico_nis
 * @property  \Carbon\Carbon $cadunico_update_date
 * @property  string $cadunico_family_code
 * @property  int $cadunico_family_member_count
 * @property  float $cadunico_per_capita_income
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 * @property  \Carbon\Carbon $deleted_at
 *
 * @property  string $friendly_monthly_income
 * @property  string $friendly_cadunico_per_capita_income
 *
 * @property  \App\Models\User $user
 */
class Applicant extends Model
{
    use HandlesApplicantAttributes;
    use HandlesApplicantRelationships;
    use SoftDeletes;

    protected $fillable = [
        'user_id',
        'full_name',
        'cpf',
        'rg',
        'rg_issuing_agency',
        'rg_issuing_date',
        'other_document_type',
        'other_document_number',
        'birth_date',
        'place_of_birth',
        'nationality',
        'gender',
        'marital_status',
        'marital_status_date',
        'job_title',
        'current_address_zipcode',
        'current_address_street',
        'current_address_number',
        'current_address_complement',
        'current_address_district',
        'current_address_city_id',
        'current_address_state_id',
        'monthly_income',
        'income_additional_information',
        'cadunico_nis',
        'cadunico_update_date',
        'cadunico_family_code',
        'cadunico_family_member_count',
        'cadunico_per_capita_income',
    ];
}
