<?php

namespace App\Models\Concerns\Applicant;

use App\Models\ApplicantContact;
use App\Models\ApplicantDependant;
use App\Models\ApplicantSpouse;
use App\Models\User;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

trait HandlesApplicantRelationships
{
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function applicantContacts(): HasMany
    {
        return $this->hasMany(ApplicantContact::class)
            ->orderBy('is_primary', 'desc');
    }

    public function applicantSpouses(): HasMany
    {
        return $this->hasMany(ApplicantSpouse::class);
    }

    public function applicantDependants(): HasMany
    {
        return $this->hasMany(ApplicantDependant::class);
    }
}
