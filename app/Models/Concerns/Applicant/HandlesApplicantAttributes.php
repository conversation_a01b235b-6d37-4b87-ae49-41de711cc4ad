<?php

namespace App\Models\Concerns\Applicant;

trait HandlesApplicantAttributes
{
    public function setMonthlyIncomeAttribute($value): void
    {
        $this->attributes['monthly_income'] = unmask_money($value);
    }

    public function getFriendlyMonthlyIncomeAttribute(): string
    {
        return format_money($this->monthly_income);
    }

    public function setCadunicoPerCapitaIncomeAttribute($value): void
    {
        $this->attributes['cadunico_per_capita_income'] = unmask_money($value);
    }

    public function getFriendlyCadunicoPerCapitaIncomeAttribute(): string
    {
        return format_money($this->cadunico_per_capita_income);
    }
}
