<?php

namespace App\Enums;

enum ApplicantOtherDocumentTypeEnum: string
{
    case CNH = 'CNH';
    case PASSPORT = 'PASSPORT';
    case RESIDENCE_PERMIT = 'RESIDENCE_PERMIT';
    case VOTER_ID = 'VOTER_ID';
    case MILITARY_ID = 'MILITARY_ID';
    case OTHER = 'OTHER';

    public function label(): string
    {
        return match ($this) {
            self::CNH => 'Carteira Nacional de Habilitação (CNH)',
            self::PASSPORT => 'Passaporte',
            self::RESIDENCE_PERMIT => 'Permissão de Residência',
            self::VOTER_ID => 'Título de Eleitor',
            self::MILITARY_ID => 'Identidade Militar',
            self::OTHER => 'Outro',
        };
    }

    public static function getTranslated(): array
    {
        return [
            self::CNH->value => self::CNH->label(),
            self::PASSPORT->value => self::PASSPORT->label(),
            self::RESIDENCE_PERMIT->value => self::RESIDENCE_PERMIT->label(),
            self::VOTER_ID->value => self::VOTER_ID->label(),
            self::MILITARY_ID->value => self::MILITARY_ID->label(),
            self::OTHER->value => self::OTHER->label(),
        ];
    }
}
