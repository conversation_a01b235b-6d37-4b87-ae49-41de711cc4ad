<?php

namespace App\Enums;

enum PlotMainUseEnum: string
{
    case RESIDENTIAL = 'RESIDENTIAL';
    case COMMERCIAL = 'COMMERCIAL';
    case AGRICULTURAL = 'AGRICULTURAL';
    case INDUSTRIAL = 'INDUSTRIAL';
    case OTHER = 'OTHER';

    public function label(): string
    {
        return match ($this) {
            self::RESIDENTIAL => 'Residencial',
            self::COMMERCIAL => 'Comercial',
            self::AGRICULTURAL => 'Agrícola',
            self::INDUSTRIAL => 'Industrial',
            self::OTHER => 'Outro',
        };
    }

    public static function getTranslated(): array
    {
        return [
            self::RESIDENTIAL->value => self::RESIDENTIAL->label(),
            self::COMMERCIAL->value => self::COMMERCIAL->label(),
            self::AGRICULTURAL->value => self::AGRICULTURAL->label(),
            self::INDUSTRIAL->value => self::INDUSTRIAL->label(),
            self::OTHER->value => self::OTHER->label(),
        ];
    }
}
