<?php

namespace App\Enums;

enum ApplicantMaritalStatusEnum: string
{
    case SINGLE = 'SINGLE';
    case MARRIED = 'MARRIED';
    case DIVORCED = 'DIVORCED';
    case WIDOWED = 'WIDOWED';
    case SEPARATED = 'SEPARATED';

    public function label(): string
    {
        return match ($this) {
            self::SINGLE => 'Sol<PERSON>iro(a)',
            self::MARRIED => 'Casado(a)',
            self::DIVORCED => 'Divorciado(a)',
            self::WIDOWED => 'Viúvo(a)',
            self::SEPARATED => 'Separado(a)',
        };
    }

    public static function getTranslated(): array
    {
        return [
            self::SINGLE->value => self::SINGLE->label(),
            self::MARRIED->value => self::MARRIED->label(),
            self::DIVORCED->value => self::DIVORCED->label(),
            self::WIDOWED->value => self::WIDOWED->label(),
            self::SEPARATED->value => self::SEPARATED->label(),
        ];
    }
}
