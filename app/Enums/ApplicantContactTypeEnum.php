<?php

namespace App\Enums;

enum ApplicantContactTypeEnum: string
{
    case PHONE = 'phone';
    case EMAIL = 'email';

    public function label(): string
    {
        return match ($this) {
            self::PHONE => 'Telefone',
            self::EMAIL => 'E-mail',
        };
    }

    public static function getTranslated(): array
    {
        return [
            self::PHONE->value => self::PHONE->label(),
            self::EMAIL->value => self::EMAIL->label(),
        ];
    }
}
