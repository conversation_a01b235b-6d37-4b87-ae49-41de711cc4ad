<?php

namespace App\Enums;

enum InformalUrbanAreaReurbTypeEnum: string
{
    case REURB_S = 'REURB_S';
    case REURB_E = 'REURB_E';

    public function label(): string
    {
        return match ($this) {
            self::REURB_S => 'REURB-S',
            self::REURB_E => 'REURB-E',
        };
    }

    public static function getTranslated(): array
    {
        return [
            self::REURB_S->value => self::REURB_S->label(),
            self::REURB_E->value => self::REURB_E->label(),
        ];
    }
}
