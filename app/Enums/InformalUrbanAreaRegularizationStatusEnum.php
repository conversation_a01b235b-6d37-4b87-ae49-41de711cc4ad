<?php

namespace App\Enums;

enum InformalUrbanAreaRegularizationStatusEnum: string
{
    case IN_ANALYSIS = 'IN_ANALYSIS';

    public function label(): string
    {
        return match ($this) {
            self::IN_ANALYSIS => 'Em análise',
        };
    }

    public static function getTranslated(): array
    {
        return [
            self::IN_ANALYSIS->value => self::IN_ANALYSIS->label(),
        ];
    }
}
