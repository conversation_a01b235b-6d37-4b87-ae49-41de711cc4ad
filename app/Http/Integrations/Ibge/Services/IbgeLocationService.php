<?php

namespace App\Http\Integrations\Ibge\Services;

use App\Http\Integrations\Ibge\IbgeConnector;
use App\Http\Integrations\Ibge\Requests\City\IbgeGetCitiesRequest;
use App\Http\Integrations\Ibge\Requests\Region\IbgeGetRegionsRequest;
use App\Http\Integrations\Ibge\Requests\State\IbgeGetStatesRequest;

class IbgeLocationService
{
    private IbgeConnector $ibgeConnector;

    public function __construct()
    {
        $this->ibgeConnector = new IbgeConnector();
    }

    public function getRegions(): mixed
    {
        $request = new IbgeGetRegionsRequest();
        $response = $this->ibgeConnector->send($request);

        return json_decode($response->body());
    }

    public function getStates(): mixed
    {
        $request = new IbgeGetStatesRequest();
        $response = $this->ibgeConnector->send($request);

        return json_decode($response->body());
    }

    public function getCities(): mixed
    {
        $request = new IbgeGetCitiesRequest();
        $response = $this->ibgeConnector->send($request);

        return json_decode($response->body());
    }
}
