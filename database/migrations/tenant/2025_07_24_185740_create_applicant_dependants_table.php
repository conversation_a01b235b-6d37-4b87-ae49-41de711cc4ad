<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('applicant_dependants', function (Blueprint $table) {
            $table->id();
            $table->foreignId('applicant_id')->constrained()->onDelete('cascade');
            $table->string('full_name');
            $table->string('cpf', 14)->unique();
            $table->string('rg', 20)->nullable();
            $table->string('rg_issuing_agency', 50)->nullable();
            $table->date('rg_issuing_date')->nullable();
            $table->string('other_document_type')->nullable();
            $table->string('other_document_number', 50)->nullable();
            $table->string('gender', 1)->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('applicant_dependants');
    }
};
