<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('informal_urban_areas', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('identification_code', 50)->unique()->nullable();
            $table->text('description')->nullable();
            $table->string('regularization_status', 50);
            $table->string('reurb_type', 10);
            $table->date('estimated_creation_date')->nullable();
            $table->foreignId('city_id');
            $table->foreignId('state_id');
            $table->string('main_district')->nullable();
            $table->decimal('total_area_m2', 15, 2)->nullable();
            $table->text('additional_information')->nullable();
            $table->boolean('water_infrastructure_available')->default(false);
            $table->boolean('sanitary_infrastructure_available')->default(false);
            $table->boolean('eletricity_infrastructure_available')->default(false);
            $table->boolean('road_infrastructure_available')->default(false);
            $table->text('infrastructure_additional_information')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('informal_urban_areas');
    }
};
