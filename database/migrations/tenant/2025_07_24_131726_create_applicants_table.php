<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('applicants', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->nullable()->constrained();
            $table->string('full_name');
            $table->string('cpf', 14)->unique();
            $table->string('rg', 20)->nullable();
            $table->string('rg_issuing_agency', 50)->nullable();
            $table->date('rg_issuing_date')->nullable();
            $table->string('other_document_type')->nullable();
            $table->string('other_document_number', 50)->nullable();
            $table->date('birth_date')->nullable();
            $table->string('place_of_birth')->nullable();
            $table->string('nationality')->default('Brasileira');
            $table->string('gender', 1)->nullable();
            $table->string('marital_status', 20)->nullable();
            $table->date('marital_status_date')->nullable();
            $table->string('job_title')->nullable();
            $table->string('current_address_zipcode', 9)->nullable();
            $table->string('current_address_street')->nullable();
            $table->string('current_address_number', 10)->nullable();
            $table->string('current_address_complement')->nullable();
            $table->string('current_address_district')->nullable();
            $table->foreignId('current_address_city_id')->nullable();
            $table->string('current_address_city')->nullable();
            $table->foreignId('current_address_state_id')->nullable();
            $table->string('current_address_state')->nullable();

            // Income information (speficic for Reurb-S)
            $table->decimal('monthly_income', 10, 2)->nullable();
            $table->text('income_additional_information')->nullable();

            // Cadastro Único (CadÚnico) details
            $table->string('cadunico_nis', 15)->nullable();
            $table->date('cadunico_update_date')->nullable();
            $table->string('cadunico_family_code', 20)->nullable();
            $table->integer('cadunico_family_member_count')->nullable();
            $table->decimal('cadunico_per_capita_income', 10, 2)->nullable();

            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('applicants');
    }
};
