<?php

use App\Enums\PlotMainUseEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plots', function (Blueprint $table) {
            $table->id();
            $table->foreignId('informal_urban_area_id')->constrained();
            $table->foreignId('main_applicant_id')->constrained('applicants');

            $table->string('identification', 50)->unique();
            $table->string('original_registration_number')->nullable();
            $table->string('property_registry_office')->nullable();

            $table->string('zipcode')->nullable();
            $table->string('street')->nullable();
            $table->string('number')->nullable();
            $table->string('complement')->nullable();
            $table->string('district')->nullable();
            $table->foreignId('city_id');
            $table->foreignId('state_id');

            $table->decimal('total_area_m2', 15, 2);
            $table->json('coordinates')->nullable();
            $table->string('iptu_tax_zone')->nullable();
            $table->json('iptu_city_registrations')->nullable();

            $table->date('occupation_estimated_date')->nullable();

            $table->string('main_use', 20)->default(PlotMainUseEnum::RESIDENTIAL->value);
            $table->boolean('has_buildings')->default(false);

            $table->boolean('belongs_to_risk_area')->default(false);
            $table->text('risk_area_description')->nullable();
            $table->boolean('belongs_to_permanent_protection_area')->default(false);
            $table->text('permanent_protection_area_description')->nullable();
            $table->json('other_restrictions')->nullable();
            $table->text('additional_information')->nullable();

            $table->boolean('water_infrastructure_available')->default(false);
            $table->boolean('sanitary_infrastructure_available')->default(false);
            $table->boolean('eletricity_infrastructure_available')->default(false);
            $table->boolean('road_infrastructure_available')->default(false);

            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plots');
    }
};
