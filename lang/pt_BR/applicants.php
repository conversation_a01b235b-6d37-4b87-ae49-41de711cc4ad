<?php

return [

    'forms' => [
        'fields' => [
            'full_name' => 'Nome completo',
            'cpf' => 'CPF',
            'rg' => 'RG',
            'rg_issuing_agency' => 'Orgão emissor do RG',
            'rg_issuing_date' => 'Data de emissão do RG',
            'other_document_type' => 'Tipo de documento',
            'other_document_number' => 'Número do documento',
            'birth_date' => 'Data de nascimento',
            'place_of_birth' => 'Naturalidade',
            'nationality' => 'Nacionalidade',
            'gender' => 'Gênero',
            'marital_status' => 'Estado civil',
            'marital_status_date' => 'Data do estado civil',
            'job_title' => 'Profissão',
            'current_address_zipcode' => 'CEP',
            'current_address_street' => 'Logradouro',
            'current_address_number' => 'Número',
            'current_address_complement' => 'Complemento',
            'current_address_district' => 'Bairro',
            'current_address_city_id' => 'Cidade',
            'current_address_state_id' => 'Estado',
            'monthly_income' => 'Renda mensal',
            'income_additional_information' => 'Informações adicionais sobre a renda',
            'cadunico_nis' => 'NIS (Cadastro Único)',
            'cadunico_update_date' => 'Data da última atualização',
            'cadunico_family_code' => 'Código da família',
            'cadunico_family_member_count' => 'Quantidade de membros da família',
            'cadunico_per_capita_income' => 'Renda per capita',
        ],
    ],

    'responses' => [
        'create' => [
            'success' => 'O requerente foi criado.',
            'error' => 'Erro ao criar o requerente. Por favor, tente novamente mais tarde.',
        ],
        'update' => [
            'success' => 'O requerente foi atualizado.',
            'error' => 'Erro ao atualizar o requerente. Por favor, tente novamente mais tarde.',
        ],
        'delete' => [
            'success' => 'O requerente foi excluído.',
            'error' => 'Erro ao deletar o requerente. Por favor, tente novamente mais tarde.',
        ],
    ],

];
